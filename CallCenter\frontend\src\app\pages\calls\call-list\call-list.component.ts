import { Component, OnInit, OnD<PERSON>roy, ChangeDetectionStrategy } from '@angular/core';
import { Subject, takeUntil, debounceTime, distinctUntilChanged } from 'rxjs';
import { CallTableComponent } from "./call-table/call-table.component";
import { CallFilterComponent } from "./call-filter/call-filter.component";
import { CallService } from '../../../core/services/call.service';
import { Call, CallFilter, CallListResponse } from '../../../shared/models/call.interface';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-call-list',
  standalone: true,
  imports: [CallTableComponent, CallFilterComponent,CommonModule],
  templateUrl: './call-list.component.html',
  styleUrl: './call-list.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class CallListComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();

  calls: Call[] = [];
  loading = false;
  error: string | null = null;
  totalCalls = 0;
  currentPage = 1;
  pageSize = 20;
  currentFilter: CallFilter = {};

  constructor(private callService: CallService) {}

  ngOnInit(): void {
    this.loadCalls();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  onFilterChange(filter: CallFilter): void {
    this.currentFilter = filter;
    this.currentPage = 1;
    this.loadCalls();
  }

  onPageChange(page: number): void {
    this.currentPage = page;
    this.loadCalls();
  }

  private loadCalls(): void {
    this.loading = true;
    this.error = null;

    this.callService.getCalls(this.currentPage, this.pageSize, this.currentFilter)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response: CallListResponse) => {
          this.calls = response.calls;
          this.totalCalls = response.total;
          this.loading = false;
        },
        error: (error) => {
          this.error = 'Không thể tải danh sách cuộc gọi. Vui lòng thử lại.';
          this.loading = false;
          console.error('Error loading calls:', error);
        }
      });
  }

  refreshCalls(): void {
    this.loadCalls();
  }
}
