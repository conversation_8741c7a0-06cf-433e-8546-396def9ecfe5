<div class="bg-white shadow-sm rounded-lg overflow-hidden">
  <!-- Table -->
  <div class="overflow-x-auto">
    <table class="min-w-full divide-y divide-gray-200">
      <thead class="bg-gray-50">
        <tr>
          <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
            Thời gian
          </th>
          <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
            Khách hàng
          </th>
          <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
            Nhân viên
          </th>
          <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
            Thời l<PERSON>ợng
          </th>
          <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
            H<PERSON>ớng
          </th>
          <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
            Tr<PERSON><PERSON> thái
          </th>
          <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
            QA
          </th>
          <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
            Điểm QA
          </th>
          <th class="relative px-6 py-3">
            <span class="sr-only">Hành động</span>
          </th>
        </tr>
      </thead>
      <tbody class="bg-white divide-y divide-gray-200">
        <tr *ngFor="let call of calls; trackBy: trackByCallId"
            class="hover:bg-gray-50 transition-colors">
          <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
            {{ formatDate(call.startTime, 'dd/MM/yyyy HH:mm') }}
          </td>
          <td class="px-6 py-4 whitespace-nowrap">
            <div class="text-sm font-medium text-gray-900">{{ call.customerName }}</div>
            <div class="text-sm text-gray-500">{{ call.customerPhone }}</div>
          </td>
          <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
            {{ call.agentName }}
          </td>
          <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
            {{ formatDuration(call.duration) }}
          </td>
          <td class="px-6 py-4 whitespace-nowrap">
            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                  [ngClass]="call.direction === 'inbound' ? 'bg-blue-100 text-blue-800' : 'bg-green-100 text-green-800'">
              {{ directionLabels[call.direction] }}
            </span>
          </td>
          <td class="px-6 py-4 whitespace-nowrap">
            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                  [ngClass]="getStatusClass(call.status)">
              {{ statusLabels[call.status] }}
            </span>
          </td>
          <td class="px-6 py-4 whitespace-nowrap">
            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                  [ngClass]="getQAStatusClass(call.qaStatus)">
              {{ qaStatusLabels[call.qaStatus] }}
            </span>
          </td>
          <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
            <span *ngIf="call.qaScore !== undefined"
                  class="font-medium"
                  [ngClass]="call.qaScore >= 80 ? 'text-green-600' : call.qaScore >= 60 ? 'text-yellow-600' : 'text-red-600'">
              {{ call.qaScore }}%
            </span>
            <span *ngIf="call.qaScore === undefined" class="text-gray-400">-</span>
          </td>
          <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
            <a [routerLink]="['/calls', call.id]"
               class="text-blue-600 hover:text-blue-900 transition-colors">
              Xem chi tiết
            </a>
          </td>
        </tr>
      </tbody>
    </table>
  </div>

  <!-- Loading overlay -->
  <div *ngIf="loading" class="absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center">
    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
  </div>

  <!-- Pagination -->
  <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
    <div class="flex-1 flex justify-between sm:hidden">
      <button (click)="onPageClick(currentPage - 1)"
              [disabled]="currentPage <= 1"
              class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
        Trước
      </button>
      <button (click)="onPageClick(currentPage + 1)"
              [disabled]="currentPage >= totalPages"
              class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
        Sau
      </button>
    </div>
    <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
      <div>
        <p class="text-sm text-gray-700">
          Hiển thị <span class="font-medium">{{ startIndex }}</span> đến <span class="font-medium">{{ endIndex }}</span>
          trong tổng số <span class="font-medium">{{ totalCalls }}</span> kết quả
        </p>
      </div>
      <div>
        <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
          <button (click)="onPageClick(currentPage - 1)"
                  [disabled]="currentPage <= 1"
                  class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
            <span class="sr-only">Trang trước</span>
            ←
          </button>

          <ng-container *ngFor="let page of getPageNumbers(); trackBy: trackByPage">
            <button (click)="onPageClick(page)"
                    [class]="page === currentPage ?
                      'z-10 bg-blue-50 border-blue-500 text-blue-600 relative inline-flex items-center px-4 py-2 border text-sm font-medium' :
                      'bg-white border-gray-300 text-gray-500 hover:bg-gray-50 relative inline-flex items-center px-4 py-2 border text-sm font-medium'">
              {{ page }}
            </button>
          </ng-container>

          <button (click)="onPageClick(currentPage + 1)"
                  [disabled]="currentPage >= totalPages"
                  class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
            <span class="sr-only">Trang sau</span>
            →
          </button>
        </nav>
      </div>
    </div>
  </div>
</div>
