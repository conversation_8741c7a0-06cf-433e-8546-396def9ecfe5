export const APP_CONSTANTS = {
  // Pagination
  DEFAULT_PAGE_SIZE: 20,
  PAGE_SIZE_OPTIONS: [10, 20, 50, 100],
  
  // Date formats
  DATE_FORMAT: 'dd/MM/yyyy',
  DATETIME_FORMAT: 'dd/MM/yyyy HH:mm:ss',
  TIME_FORMAT: 'HH:mm:ss',
  
  // Call duration limits (in seconds)
  MIN_CALL_DURATION: 5,
  MAX_CALL_DURATION: 7200, // 2 hours
  
  // QA Score ranges
  QA_SCORE_RANGES: {
    EXCELLENT: { min: 90, max: 100, label: 'Xuất sắc', color: '#52c41a' },
    GOOD: { min: 80, max: 89, label: 'Tốt', color: '#1890ff' },
    AVERAGE: { min: 70, max: 79, label: 'Trung bình', color: '#faad14' },
    POOR: { min: 60, max: 69, label: 'Kém', color: '#fa8c16' },
    FAIL: { min: 0, max: 59, label: 'Không đạt', color: '#f5222d' }
  },
  
  // File upload limits
  MAX_FILE_SIZE: 10 * 1024 * 1024, // 10MB
  ALLOWED_FILE_TYPES: ['.mp3', '.wav', '.m4a', '.pdf', '.doc', '.docx'],
  
  // Local storage keys
  STORAGE_KEYS: {
    USER_PREFERENCES: 'callcenter_user_preferences',
    FILTER_STATE: 'callcenter_filter_state',
    THEME: 'callcenter_theme'
  },
  
  // API endpoints
  API_ENDPOINTS: {
    CALLS: '/calls',
    QA: '/qa',
    AGENTS: '/agents',
    REPORTS: '/reports',
    UPLOAD: '/upload'
  },
  
  // Notification types
  NOTIFICATION_TYPES: {
    SUCCESS: 'success',
    ERROR: 'error',
    WARNING: 'warning',
    INFO: 'info'
  },
  
  // Chart colors
  CHART_COLORS: [
    '#1890ff', '#52c41a', '#faad14', '#f5222d', '#722ed1',
    '#fa8c16', '#13c2c2', '#eb2f96', '#a0d911', '#2f54eb'
  ]
} as const;

export const CALL_STATUS_LABELS = {
  completed: 'Hoàn thành',
  missed: 'Nhỡ cuộc gọi',
  abandoned: 'Bỏ cuộc',
  transferred: 'Chuyển tiếp'
} as const;

export const CALL_DIRECTION_LABELS = {
  inbound: 'Gọi đến',
  outbound: 'Gọi đi'
} as const;

export const QA_STATUS_LABELS = {
  pending: 'Chờ đánh giá',
  in_progress: 'Đang đánh giá',
  completed: 'Đã hoàn thành',
  failed: 'Thất bại'
} as const;

export const QA_CATEGORY_LABELS = {
  communication: 'Giao tiếp',
  product_knowledge: 'Kiến thức sản phẩm',
  problem_solving: 'Giải quyết vấn đề',
  compliance: 'Tuân thủ quy định',
  customer_satisfaction: 'Hài lòng khách hàng'
} as const;
