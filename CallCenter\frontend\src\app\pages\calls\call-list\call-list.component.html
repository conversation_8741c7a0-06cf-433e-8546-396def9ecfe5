<div class="call-list-container p-6">
    <!-- Header -->
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-2xl font-semibold text-gray-900">Danh sách cuộc gọi</h1>
        <button
            class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            (click)="refreshCalls()"
            [disabled]="loading">
            <span *ngIf="!loading">Làm mới</span>
            <span *ngIf="loading">Đang tải...</span>
        </button>
    </div>

    <!-- Filter -->
    <app-call-filter
        (filterChange)="onFilterChange($event)"
        class="mb-6 block">
    </app-call-filter>

    <!-- Error Message -->
    <div *ngIf="error" class="mb-4 p-4 bg-red-50 border border-red-200 rounded-lg">
        <div class="flex">
            <div class="text-red-800">
                <p>{{ error }}</p>
                <button
                    class="mt-2 text-sm text-red-600 hover:text-red-800 underline"
                    (click)="refreshCalls()">
                    Thử lại
                </button>
            </div>
        </div>
    </div>

    <!-- Loading State -->
    <div *ngIf="loading && calls.length === 0" class="text-center py-12">
        <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
        <p class="mt-4 text-gray-600">Đang tải danh sách cuộc gọi...</p>
    </div>

    <!-- Call Table -->
    <app-call-table
        *ngIf="!loading || calls.length > 0"
        [calls]="calls"
        [loading]="loading"
        [totalCalls]="totalCalls"
        [currentPage]="currentPage"
        [pageSize]="pageSize"
        (pageChange)="onPageChange($event)">
    </app-call-table>

    <!-- Empty State -->
    <div *ngIf="!loading && calls.length === 0 && !error" class="text-center py-12">
        <div class="text-gray-400 text-6xl mb-4">📞</div>
        <h3 class="text-lg font-medium text-gray-900 mb-2">Không có cuộc gọi nào</h3>
        <p class="text-gray-600">Không tìm thấy cuộc gọi nào phù hợp với bộ lọc hiện tại.</p>
    </div>
</div>