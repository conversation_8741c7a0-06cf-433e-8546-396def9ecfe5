import { Component, Input, Output, EventEmitter, ChangeDetectionStrategy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { Call } from '../../../../shared/models/call.interface';
import { DateUtils } from '../../../../shared/utils/date.utils';
import { CALL_STATUS_LABELS, CALL_DIRECTION_LABELS, QA_STATUS_LABELS } from '../../../../shared/constants/app.constants';

@Component({
  selector: 'app-call-table',
  standalone: true,
  imports: [CommonModule, RouterModule],
  templateUrl: './call-table.component.html',
  styleUrl: './call-table.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class CallTableComponent {
  @Input() calls: Call[] = [];
  @Input() loading = false;
  @Input() totalCalls = 0;
  @Input() currentPage = 1;
  @Input() pageSize = 20;

  @Output() pageChange = new EventEmitter<number>();

  readonly statusLabels = CALL_STATUS_LABELS;
  readonly directionLabels = CALL_DIRECTION_LABELS;
  readonly qaStatusLabels = QA_STATUS_LABELS;

  formatDate = DateUtils.formatDate;
  formatDuration = DateUtils.formatDuration;

  get totalPages(): number {
    return Math.ceil(this.totalCalls / this.pageSize);
  }

  get startIndex(): number {
    return (this.currentPage - 1) * this.pageSize + 1;
  }

  get endIndex(): number {
    return Math.min(this.currentPage * this.pageSize, this.totalCalls);
  }

  onPageClick(page: number): void {
    if (page !== this.currentPage && page >= 1 && page <= this.totalPages) {
      this.pageChange.emit(page);
    }
  }

  getStatusClass(status: string): string {
    const statusClasses = {
      completed: 'bg-green-100 text-green-800',
      missed: 'bg-red-100 text-red-800',
      abandoned: 'bg-yellow-100 text-yellow-800',
      transferred: 'bg-blue-100 text-blue-800'
    };
    return statusClasses[status as keyof typeof statusClasses] || 'bg-gray-100 text-gray-800';
  }

  getQAStatusClass(status: string): string {
    const statusClasses = {
      pending: 'bg-yellow-100 text-yellow-800',
      in_progress: 'bg-blue-100 text-blue-800',
      completed: 'bg-green-100 text-green-800',
      failed: 'bg-red-100 text-red-800'
    };
    return statusClasses[status as keyof typeof statusClasses] || 'bg-gray-100 text-gray-800';
  }

  trackByCallId(index: number, call: Call): string {
    return call.id;
  }

  trackByPage(index: number, page: number): number {
    return page;
  }

  getPageNumbers(): number[] {
    const pages: number[] = [];
    const maxVisiblePages = 5;
    const halfVisible = Math.floor(maxVisiblePages / 2);

    let startPage = Math.max(1, this.currentPage - halfVisible);
    let endPage = Math.min(this.totalPages, startPage + maxVisiblePages - 1);

    // Adjust start page if we're near the end
    if (endPage - startPage < maxVisiblePages - 1) {
      startPage = Math.max(1, endPage - maxVisiblePages + 1);
    }

    for (let i = startPage; i <= endPage; i++) {
      pages.push(i);
    }

    return pages;
  }
}
