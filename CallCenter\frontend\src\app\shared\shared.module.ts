import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormsModule } from '@angular/forms';

// Re-export utilities and constants
export * from './constants/app.constants';
export * from './utils/date.utils';
export * from './utils/validation.utils';
export * from './models/call.interface';
export * from './models/qa.interface';
export * from './models/agent.interface';

@NgModule({
  imports: [
    CommonModule,
    ReactiveFormsModule,
    FormsModule
  ],
  exports: [
    CommonModule,
    ReactiveFormsModule,
    FormsModule
  ]
})
export class SharedModule { }
