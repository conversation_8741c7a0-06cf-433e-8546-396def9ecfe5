import { AbstractControl, ValidationErrors, ValidatorFn } from '@angular/forms';

export class ValidationUtils {
  /**
   * Phone number validator for Vietnamese phone numbers
   */
  static phoneValidator(): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      if (!control.value) return null;
      
      const phoneRegex = /^(\+84|84|0)(3[2-9]|5[689]|7[06-9]|8[1-689]|9[0-46-9])[0-9]{7}$/;
      return phoneRegex.test(control.value) ? null : { invalidPhone: true };
    };
  }

  /**
   * Email validator
   */
  static emailValidator(): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      if (!control.value) return null;
      
      const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
      return emailRegex.test(control.value) ? null : { invalidEmail: true };
    };
  }

  /**
   * Score range validator (0-100)
   */
  static scoreValidator(min: number = 0, max: number = 100): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      if (!control.value && control.value !== 0) return null;
      
      const value = Number(control.value);
      if (isNaN(value)) return { invalidScore: true };
      if (value < min || value > max) return { scoreOutOfRange: { min, max, actual: value } };
      
      return null;
    };
  }

  /**
   * Duration validator (in seconds)
   */
  static durationValidator(minSeconds: number = 0, maxSeconds: number = 7200): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      if (!control.value && control.value !== 0) return null;
      
      const value = Number(control.value);
      if (isNaN(value)) return { invalidDuration: true };
      if (value < minSeconds || value > maxSeconds) {
        return { durationOutOfRange: { min: minSeconds, max: maxSeconds, actual: value } };
      }
      
      return null;
    };
  }

  /**
   * Date range validator
   */
  static dateRangeValidator(startDateControlName: string, endDateControlName: string): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      const startDate = control.get(startDateControlName)?.value;
      const endDate = control.get(endDateControlName)?.value;
      
      if (!startDate || !endDate) return null;
      
      const start = new Date(startDate);
      const end = new Date(endDate);
      
      if (start > end) {
        return { dateRangeInvalid: true };
      }
      
      return null;
    };
  }

  /**
   * File size validator
   */
  static fileSizeValidator(maxSizeInBytes: number): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      if (!control.value) return null;
      
      const file = control.value as File;
      if (file && file.size > maxSizeInBytes) {
        return { fileSizeExceeded: { maxSize: maxSizeInBytes, actualSize: file.size } };
      }
      
      return null;
    };
  }

  /**
   * File type validator
   */
  static fileTypeValidator(allowedTypes: string[]): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      if (!control.value) return null;
      
      const file = control.value as File;
      if (file) {
        const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase();
        if (!allowedTypes.includes(fileExtension)) {
          return { invalidFileType: { allowedTypes, actualType: fileExtension } };
        }
      }
      
      return null;
    };
  }

  /**
   * Get error message for validation errors
   */
  static getErrorMessage(errors: ValidationErrors): string {
    if (errors['required']) {
      return 'Trường này là bắt buộc';
    }
    
    if (errors['invalidPhone']) {
      return 'Số điện thoại không hợp lệ';
    }
    
    if (errors['invalidEmail']) {
      return 'Email không hợp lệ';
    }
    
    if (errors['invalidScore']) {
      return 'Điểm số không hợp lệ';
    }
    
    if (errors['scoreOutOfRange']) {
      const { min, max } = errors['scoreOutOfRange'];
      return `Điểm số phải từ ${min} đến ${max}`;
    }
    
    if (errors['invalidDuration']) {
      return 'Thời lượng không hợp lệ';
    }
    
    if (errors['durationOutOfRange']) {
      const { min, max } = errors['durationOutOfRange'];
      return `Thời lượng phải từ ${min} đến ${max} giây`;
    }
    
    if (errors['dateRangeInvalid']) {
      return 'Ngày bắt đầu phải nhỏ hơn ngày kết thúc';
    }
    
    if (errors['fileSizeExceeded']) {
      const { maxSize } = errors['fileSizeExceeded'];
      return `Kích thước file không được vượt quá ${Math.round(maxSize / 1024 / 1024)}MB`;
    }
    
    if (errors['invalidFileType']) {
      const { allowedTypes } = errors['invalidFileType'];
      return `Chỉ chấp nhận các loại file: ${allowedTypes.join(', ')}`;
    }
    
    if (errors['minlength']) {
      const { requiredLength } = errors['minlength'];
      return `Tối thiểu ${requiredLength} ký tự`;
    }
    
    if (errors['maxlength']) {
      const { requiredLength } = errors['maxlength'];
      return `Tối đa ${requiredLength} ký tự`;
    }
    
    return 'Dữ liệu không hợp lệ';
  }
}
